# 🌐 Docker容器云管理系统 - 主控端

## 📋 说明

这个文件夹将用于存放主控端相关文件。

主控端将部署在 `docker.ly-y.cn`，提供Web管理界面和API接口。

## 🎯 主控端功能规划

### 🖥️ Web管理界面
- 节点管理面板
- 容器监控界面
- 系统状态展示
- 操作日志查看

### 🔌 API接口
- 被控端心跳接收
- 命令下发接口
- 结果收集接口
- 连接测试接口

### 🗄️ 数据存储
- 节点信息管理
- 容器状态记录
- 操作历史日志
- 监控数据存储

## 📁 预期文件结构

```
web/
├── index.php              # 主页面
├── api/                   # API接口目录
│   ├── heartbeat.php      # 心跳接收接口
│   ├── poll_command.php   # 命令轮询接口
│   ├── report_result.php  # 结果报告接口
│   └── test_connection.php # 连接测试接口
├── admin/                 # 管理界面
│   ├── dashboard.php      # 仪表板
│   ├── nodes.php          # 节点管理
│   ├── containers.php     # 容器管理
│   └── logs.php           # 日志查看
├── assets/                # 静态资源
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   └── images/            # 图片资源
├── config/                # 配置文件
│   ├── database.php       # 数据库配置
│   └── settings.php       # 系统设置
└── includes/              # 公共文件
    ├── auth.php           # 认证处理
    ├── database.php       # 数据库操作
    └── functions.php      # 公共函数
```

## 🚀 技术栈

### 后端技术
- **语言**: PHP 8.2
- **数据库**: MySQL 8.0
- **Web服务器**: Nginx
- **SSL证书**: Let's Encrypt

### 前端技术
- **基础**: HTML5 + CSS3 + JavaScript
- **框架**: 原生JavaScript（按用户要求）
- **UI库**: Bootstrap 5
- **图表**: Chart.js

### 部署环境
- **域名**: docker.ly-y.cn
- **协议**: HTTPS
- **服务器**: Ubuntu 22.04 LTS

## 📊 数据库设计

### 节点表 (nodes)
- id, hostname, ip_address, auth_user, auth_pass
- status, last_heartbeat, created_at, updated_at

### 容器表 (containers)
- id, node_id, container_id, name, image, status
- cpu_limit, memory_limit, created_at, updated_at

### 命令表 (commands)
- id, node_id, command_type, command, params
- status, result, created_at, executed_at

### 日志表 (logs)
- id, node_id, level, message, data
- created_at

## 🔐 安全设计

### 认证机制
- 被控端账号密码认证
- 管理员登录认证
- API接口权限控制

### 数据安全
- HTTPS加密传输
- 数据库连接加密
- 敏感信息加密存储

## 📈 监控功能

### 实时监控
- 节点在线状态
- 容器运行状态
- 系统资源使用

### 历史数据
- 性能趋势图表
- 操作历史记录
- 异常事件日志

## 🛠️ 开发计划

1. **阶段1**: 基础API接口开发
2. **阶段2**: 数据库设计和实现
3. **阶段3**: Web管理界面开发
4. **阶段4**: 监控和日志功能
5. **阶段5**: 测试和部署

---

**注意**: 此文件夹目前为空，等待主控端开发。
