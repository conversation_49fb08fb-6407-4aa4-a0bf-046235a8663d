#!/bin/bash

# Docker容器云管理系统 - 统一管理脚本
# 支持系统: Ubuntu 22.04 和 Ubuntu 24.04
# 作者: Docker Cloud Management System
# 下载地址: https://d.ly-y.cn/d/install_agent.sh

set -e

# 配置信息
BASE_URL="https://d.ly-y.cn/d"
SCRIPT_VERSION="1.0.0"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行，请使用sudo执行"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测系统版本"
        exit 1
    fi

    if [[ "$OS" != "Ubuntu" ]]; then
        log_error "不支持的操作系统: $OS"
        exit 1
    fi

    if [[ "$VER" != "22.04" && "$VER" != "24.04" ]]; then
        log_error "不支持的Ubuntu版本: $VER，仅支持22.04和24.04"
        exit 1
    fi

    log_success "系统检查通过: $OS $VER"
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    apt update -y
    apt upgrade -y
    log_success "系统包更新完成"
}

# 安装Python3
install_python() {
    log_info "安装Python3及相关包..."
    apt install -y python3 python3-pip python3-venv python3-dev
    
    # 检查Python版本
    PYTHON_VERSION=$(python3 --version)
    log_success "Python3安装完成: $PYTHON_VERSION"
}

# 安装Docker
install_docker() {
    log_info "安装Docker..."
    
    # 卸载旧版本
    apt remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true
    
    # 安装依赖
    apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 更新包索引并安装Docker
    apt update -y
    apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    # 检查Docker版本
    DOCKER_VERSION=$(docker --version)
    log_success "Docker安装完成: $DOCKER_VERSION"
}

# 配置Docker阿里云镜像源
configure_docker_mirror() {
    log_info "配置Docker阿里云镜像源..."
    
    mkdir -p /etc/docker
    
    cat > /etc/docker/daemon.json << EOF
{
    "registry-mirrors": [
        "https://registry.cn-hangzhou.aliyuncs.com",
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2"
}
EOF
    
    # 重启Docker服务
    systemctl daemon-reload
    systemctl restart docker
    
    log_success "Docker阿里云镜像源配置完成"
}

# 生成鉴权账号密码
generate_auth() {
    log_info "生成鉴权账号密码..."
    
    # 生成随机用户名和密码
    AUTH_USER="agent_$(openssl rand -hex 4)"
    AUTH_PASS=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-16)
    
    # 创建配置目录
    mkdir -p /opt/docker-cloud-agent
    
    # 保存认证信息
    cat > /opt/docker-cloud-agent/auth.conf << EOF
AUTH_USER=$AUTH_USER
AUTH_PASS=$AUTH_PASS
AGENT_ID=$(hostname)-$(date +%s)
MASTER_URL=https://docker.ly-y.cn
EOF
    
    chmod 600 /opt/docker-cloud-agent/auth.conf
    
    log_success "鉴权信息生成完成"
    echo "=========================="
    echo "鉴权账号: $AUTH_USER"
    echo "鉴权密码: $AUTH_PASS"
    echo "=========================="
}

# 创建被控端主程序
create_agent_program() {
    log_info "创建被控端主程序..."
    
    cat > /opt/docker-cloud-agent/agent.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import json
import hashlib
import requests
import subprocess
import threading
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/docker-cloud-agent.log'),
        logging.StreamHandler()
    ]
)

class DockerCloudAgent:
    def __init__(self):
        self.load_config()
        self.session = requests.Session()
        self.session.timeout = 30
        self.running = True
        
    def load_config(self):
        """加载配置文件"""
        config_file = '/opt/docker-cloud-agent/auth.conf'
        if not os.path.exists(config_file):
            logging.error("配置文件不存在")
            sys.exit(1)
            
        with open(config_file, 'r') as f:
            for line in f:
                if '=' in line:
                    key, value = line.strip().split('=', 1)
                    setattr(self, key.lower(), value)
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # CPU信息
            cpu_info = subprocess.check_output("nproc", shell=True).decode().strip()
            
            # 内存信息
            mem_info = subprocess.check_output("free -m | grep Mem | awk '{print $2}'", shell=True).decode().strip()
            
            # 磁盘信息
            disk_info = subprocess.check_output("df -h / | tail -1 | awk '{print $2}'", shell=True).decode().strip()
            
            # Docker版本
            docker_version = subprocess.check_output("docker --version", shell=True).decode().strip()
            
            return {
                'cpu_cores': cpu_info,
                'memory_mb': mem_info,
                'disk_size': disk_info,
                'docker_version': docker_version,
                'hostname': os.uname().nodename,
                'uptime': subprocess.check_output("uptime -p", shell=True).decode().strip()
            }
        except Exception as e:
            logging.error(f"获取系统信息失败: {e}")
            return {}
    
    def execute_command(self, command):
        """执行命令"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=300
            )
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'stdout': '',
                'stderr': '命令执行超时',
                'returncode': -1
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }
    
    def heartbeat(self):
        """心跳检测"""
        while self.running:
            try:
                data = {
                    'agent_id': self.agent_id,
                    'auth_user': self.auth_user,
                    'auth_pass': self.auth_pass,
                    'timestamp': int(time.time()),
                    'system_info': self.get_system_info()
                }
                
                response = self.session.post(
                    f"{self.master_url}/api/heartbeat",
                    json=data,
                    verify=False
                )
                
                if response.status_code == 200:
                    logging.info("心跳发送成功")
                else:
                    logging.warning(f"心跳发送失败: {response.status_code}")
                    
            except Exception as e:
                logging.error(f"心跳发送异常: {e}")
            
            time.sleep(30)  # 30秒心跳间隔
    
    def poll_commands(self):
        """轮询命令"""
        while self.running:
            try:
                data = {
                    'agent_id': self.agent_id,
                    'auth_user': self.auth_user,
                    'auth_pass': self.auth_pass,
                    'timestamp': int(time.time())
                }
                
                response = self.session.post(
                    f"{self.master_url}/api/poll_command",
                    json=data,
                    verify=False
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('has_command'):
                        command_id = result.get('command_id')
                        command = result.get('command')
                        
                        logging.info(f"收到命令: {command}")
                        
                        # 执行命令
                        exec_result = self.execute_command(command)
                        
                        # 返回执行结果
                        self.report_result(command_id, exec_result)
                        
            except Exception as e:
                logging.error(f"轮询命令异常: {e}")
            
            time.sleep(10)  # 10秒轮询间隔
    
    def report_result(self, command_id, result):
        """报告执行结果"""
        try:
            data = {
                'agent_id': self.agent_id,
                'auth_user': self.auth_user,
                'auth_pass': self.auth_pass,
                'command_id': command_id,
                'result': result,
                'timestamp': int(time.time())
            }
            
            response = self.session.post(
                f"{self.master_url}/api/report_result",
                json=data,
                verify=False
            )
            
            if response.status_code == 200:
                logging.info(f"结果报告成功: {command_id}")
            else:
                logging.warning(f"结果报告失败: {response.status_code}")
                
        except Exception as e:
            logging.error(f"结果报告异常: {e}")
    
    def run(self):
        """运行代理"""
        logging.info("Docker Cloud Agent 启动")
        
        # 启动心跳线程
        heartbeat_thread = threading.Thread(target=self.heartbeat)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()
        
        # 启动命令轮询线程
        poll_thread = threading.Thread(target=self.poll_commands)
        poll_thread.daemon = True
        poll_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logging.info("收到停止信号")
            self.running = False

if __name__ == "__main__":
    agent = DockerCloudAgent()
    agent.run()
EOF
    
    chmod +x /opt/docker-cloud-agent/agent.py
    
    log_success "被控端主程序创建完成"
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖包..."
    
    pip3 install requests
    
    log_success "Python依赖包安装完成"
}

# 创建系统服务
create_systemd_service() {
    log_info "创建系统服务..."
    
    cat > /etc/systemd/system/docker-cloud-agent.service << EOF
[Unit]
Description=Docker Cloud Agent
After=network.target docker.service
Requires=docker.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/docker-cloud-agent
ExecStart=/usr/bin/python3 /opt/docker-cloud-agent/agent.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd并启动服务
    systemctl daemon-reload
    systemctl enable docker-cloud-agent
    systemctl start docker-cloud-agent
    
    log_success "系统服务创建并启动完成"
}

# 显示安装结果
show_result() {
    echo ""
    echo "========================================"
    echo "Docker容器云管理系统被控端安装完成！"
    echo "========================================"
    echo ""
    echo "安装信息："
    echo "- Python3: $(python3 --version)"
    echo "- Docker: $(docker --version)"
    echo "- 服务状态: $(systemctl is-active docker-cloud-agent)"
    echo ""
    echo "鉴权信息："
    echo "- 账号: $AUTH_USER"
    echo "- 密码: $AUTH_PASS"
    echo ""
    echo "配置文件位置: /opt/docker-cloud-agent/auth.conf"
    echo "日志文件位置: /var/log/docker-cloud-agent.log"
    echo ""
    echo "服务管理命令："
    echo "- 查看状态: systemctl status docker-cloud-agent"
    echo "- 重启服务: systemctl restart docker-cloud-agent"
    echo "- 查看日志: journalctl -u docker-cloud-agent -f"
    echo ""
    echo "请将以上鉴权信息提供给主控端进行节点添加！"
    echo "========================================"
}

# 显示帮助信息
show_help() {
    echo "========================================"
    echo "Docker容器云管理系统 - 统一管理脚本"
    echo "版本: $SCRIPT_VERSION"
    echo "========================================"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  install     安装被控端系统"
    echo "  test        测试安装状态"
    echo "  uninstall   卸载被控端系统"
    echo "  status      查看服务状态"
    echo "  logs        查看服务日志"
    echo "  restart     重启服务"
    echo "  update      更新脚本"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0 install     # 安装被控端"
    echo "  sudo $0 test        # 测试安装"
    echo "  sudo $0 uninstall   # 卸载系统"
    echo "  $0 status           # 查看状态"
    echo ""
    echo "下载地址: $BASE_URL/install_agent.sh"
    echo "========================================"
}

# 下载文件函数
download_file() {
    local filename="$1"
    local url="$BASE_URL/$filename"
    local temp_file="/tmp/$filename"

    log_info "下载文件: $filename"

    if command -v curl >/dev/null 2>&1; then
        curl -fsSL "$url" -o "$temp_file"
    elif command -v wget >/dev/null 2>&1; then
        wget -q "$url" -O "$temp_file"
    else
        log_error "需要安装 curl 或 wget"
        return 1
    fi

    if [[ -f "$temp_file" ]]; then
        chmod +x "$temp_file"
        log_success "文件下载完成: $temp_file"
        echo "$temp_file"
    else
        log_error "文件下载失败: $filename"
        return 1
    fi
}

# 安装功能
do_install() {
    log_info "开始安装被控端系统..."

    check_root
    check_system
    update_system
    install_python
    install_docker
    configure_docker_mirror
    generate_auth
    create_agent_program
    install_python_deps
    create_systemd_service
    show_result
}

# 测试功能
do_test() {
    log_info "开始测试系统安装状态..."

    local test_script
    test_script=$(download_file "test_installation.sh")

    if [[ -f "$test_script" ]]; then
        bash "$test_script"
        rm -f "$test_script"
    else
        log_error "无法下载测试脚本"
        return 1
    fi
}

# 卸载功能
do_uninstall() {
    log_info "开始卸载被控端系统..."

    local uninstall_script
    uninstall_script=$(download_file "uninstall_agent.sh")

    if [[ -f "$uninstall_script" ]]; then
        bash "$uninstall_script"
        rm -f "$uninstall_script"
    else
        log_error "无法下载卸载脚本"
        return 1
    fi
}

# 查看状态
do_status() {
    echo "========================================"
    echo "Docker容器云管理系统 - 服务状态"
    echo "========================================"
    echo ""

    # 检查服务状态
    if systemctl list-unit-files | grep -q docker-cloud-agent; then
        echo "服务状态:"
        systemctl status docker-cloud-agent --no-pager || true
        echo ""

        echo "服务详情:"
        echo "- 启用状态: $(systemctl is-enabled docker-cloud-agent 2>/dev/null || echo '未安装')"
        echo "- 运行状态: $(systemctl is-active docker-cloud-agent 2>/dev/null || echo '未运行')"
        echo ""

        # 显示配置信息
        if [[ -f /opt/docker-cloud-agent/auth.conf ]]; then
            echo "配置信息:"
            cat /opt/docker-cloud-agent/auth.conf
            echo ""
        fi

        # 显示进程信息
        if pgrep -f docker-cloud-agent >/dev/null; then
            echo "进程信息:"
            ps aux | grep docker-cloud-agent | grep -v grep
        fi
    else
        echo "被控端服务未安装"
        echo ""
        echo "安装命令: sudo $0 install"
    fi

    echo "========================================"
}

# 查看日志
do_logs() {
    echo "========================================"
    echo "Docker容器云管理系统 - 服务日志"
    echo "========================================"
    echo ""

    if systemctl list-unit-files | grep -q docker-cloud-agent; then
        echo "系统日志 (最近20条):"
        journalctl -u docker-cloud-agent --no-pager -n 20 || true
        echo ""

        if [[ -f /var/log/docker-cloud-agent.log ]]; then
            echo "应用日志 (最近20条):"
            tail -n 20 /var/log/docker-cloud-agent.log
        fi

        echo ""
        echo "实时日志命令: journalctl -u docker-cloud-agent -f"
    else
        echo "被控端服务未安装"
    fi

    echo "========================================"
}

# 重启服务
do_restart() {
    echo "========================================"
    echo "Docker容器云管理系统 - 重启服务"
    echo "========================================"
    echo ""

    if systemctl list-unit-files | grep -q docker-cloud-agent; then
        log_info "重启被控端服务..."
        systemctl restart docker-cloud-agent
        sleep 2

        if systemctl is-active --quiet docker-cloud-agent; then
            log_success "服务重启成功"
            systemctl status docker-cloud-agent --no-pager
        else
            log_error "服务重启失败"
            journalctl -u docker-cloud-agent --no-pager -n 10
        fi
    else
        log_error "被控端服务未安装"
        echo "安装命令: sudo $0 install"
    fi

    echo "========================================"
}

# 更新脚本
do_update() {
    log_info "更新管理脚本..."

    local script_name="install_agent.sh"
    local backup_name="install_agent.sh.backup.$(date +%s)"

    # 备份当前脚本
    cp "$0" "$backup_name"
    log_info "当前脚本已备份为: $backup_name"

    # 下载新脚本
    local new_script
    new_script=$(download_file "$script_name")

    if [[ -f "$new_script" ]]; then
        cp "$new_script" "$0"
        chmod +x "$0"
        rm -f "$new_script"
        log_success "脚本更新完成"
        echo "请重新运行脚本以使用新版本"
    else
        log_error "脚本更新失败"
        return 1
    fi
}

# 主函数
main() {
    local action="${1:-help}"

    case "$action" in
        "install")
            do_install
            ;;
        "test")
            do_test
            ;;
        "uninstall")
            check_root
            do_uninstall
            ;;
        "status")
            do_status
            ;;
        "logs")
            do_logs
            ;;
        "restart")
            check_root
            do_restart
            ;;
        "update")
            do_update
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $action"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
