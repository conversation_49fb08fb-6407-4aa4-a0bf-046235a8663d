#!/bin/bash

# Docker容器云管理系统 - 一键管理脚本
# 支持系统: Ubuntu 22.04 和 Ubuntu 24.04
# 作者: Docker Cloud Management System
# 下载地址: https://d.ly-y.cn/d/docker-cloud-manager.sh

set -e

# 配置信息
BASE_URL="https://d.ly-y.cn/d"
SCRIPT_VERSION="1.0.0"
SCRIPT_NAME="docker-cloud-manager.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "========================================"
    echo "    Docker容器云管理系统"
    echo "    统一管理脚本 v$SCRIPT_VERSION"
    echo "========================================"
    echo -e "${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此操作需要root权限，请使用sudo执行"
        exit 1
    fi
}

# 检查网络连接
check_network() {
    if ! command -v curl >/dev/null 2>&1 && ! command -v wget >/dev/null 2>&1; then
        log_error "需要安装 curl 或 wget"
        log_info "安装命令: apt update && apt install -y curl"
        exit 1
    fi
    
    # 测试网络连接
    if command -v curl >/dev/null 2>&1; then
        if ! curl -s --connect-timeout 5 "$BASE_URL" >/dev/null; then
            log_warning "无法连接到下载服务器，请检查网络连接"
        fi
    fi
}

# 下载文件函数
download_file() {
    local filename="$1"
    local url="$BASE_URL/$filename"
    local temp_file="/tmp/$filename"
    
    log_info "下载文件: $filename"
    
    if command -v curl >/dev/null 2>&1; then
        if curl -fsSL "$url" -o "$temp_file"; then
            chmod +x "$temp_file"
            log_success "文件下载完成: $temp_file"
            echo "$temp_file"
        else
            log_error "文件下载失败: $filename"
            return 1
        fi
    elif command -v wget >/dev/null 2>&1; then
        if wget -q "$url" -O "$temp_file"; then
            chmod +x "$temp_file"
            log_success "文件下载完成: $temp_file"
            echo "$temp_file"
        else
            log_error "文件下载失败: $filename"
            return 1
        fi
    else
        log_error "需要安装 curl 或 wget"
        return 1
    fi
}

# 执行下载的脚本
execute_script() {
    local script_name="$1"
    local script_args="${2:-}"
    
    local script_file
    script_file=$(download_file "$script_name")
    
    if [[ -f "$script_file" ]]; then
        log_info "执行脚本: $script_name $script_args"
        bash "$script_file" $script_args
        local exit_code=$?
        rm -f "$script_file"
        return $exit_code
    else
        log_error "无法下载脚本: $script_name"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    show_banner
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  $0 [选项]"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo -e "  ${GREEN}install${NC}     安装被控端系统"
    echo -e "  ${GREEN}test${NC}        测试安装状态"
    echo -e "  ${GREEN}uninstall${NC}   卸载被控端系统"
    echo -e "  ${GREEN}status${NC}      查看服务状态"
    echo -e "  ${GREEN}logs${NC}        查看服务日志"
    echo -e "  ${GREEN}restart${NC}     重启服务"
    echo -e "  ${GREEN}update${NC}      更新管理脚本"
    echo -e "  ${GREEN}menu${NC}        显示交互式菜单"
    echo -e "  ${GREEN}help${NC}        显示此帮助信息"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  sudo $0 install     # 安装被控端"
    echo "  sudo $0 test        # 测试安装"
    echo "  sudo $0 uninstall   # 卸载系统"
    echo "  $0 status           # 查看状态"
    echo "  $0 menu             # 交互式菜单"
    echo ""
    echo -e "${YELLOW}一键安装命令:${NC}"
    echo "  curl -fsSL $BASE_URL/$SCRIPT_NAME | sudo bash -s install"
    echo ""
    echo -e "${CYAN}下载地址: $BASE_URL/$SCRIPT_NAME${NC}"
    echo "========================================"
}

# 显示交互式菜单
show_menu() {
    while true; do
        show_banner
        echo ""
        echo -e "${YELLOW}请选择操作:${NC}"
        echo ""
        echo "  1) 安装被控端系统"
        echo "  2) 测试安装状态"
        echo "  3) 查看服务状态"
        echo "  4) 查看服务日志"
        echo "  5) 重启服务"
        echo "  6) 卸载被控端系统"
        echo "  7) 更新管理脚本"
        echo "  8) 显示帮助信息"
        echo "  0) 退出"
        echo ""
        echo -n "请输入选项 [0-8]: "
        
        read -r choice
        echo ""
        
        case $choice in
            1)
                log_header "开始安装被控端系统"
                check_root
                execute_script "install_agent.sh" "install"
                ;;
            2)
                log_header "开始测试安装状态"
                execute_script "test_installation.sh"
                ;;
            3)
                log_header "查看服务状态"
                execute_script "install_agent.sh" "status"
                ;;
            4)
                log_header "查看服务日志"
                execute_script "install_agent.sh" "logs"
                ;;
            5)
                log_header "重启服务"
                check_root
                execute_script "install_agent.sh" "restart"
                ;;
            6)
                log_header "卸载被控端系统"
                check_root
                execute_script "uninstall_agent.sh"
                ;;
            7)
                log_header "更新管理脚本"
                do_update
                ;;
            8)
                show_help
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选项: $choice"
                ;;
        esac
        
        echo ""
        echo -n "按回车键继续..."
        read -r
    done
}

# 更新脚本
do_update() {
    log_info "更新管理脚本..."
    
    local backup_name="$SCRIPT_NAME.backup.$(date +%s)"
    
    # 备份当前脚本
    cp "$0" "$backup_name"
    log_info "当前脚本已备份为: $backup_name"
    
    # 下载新脚本
    local new_script
    new_script=$(download_file "$SCRIPT_NAME")
    
    if [[ -f "$new_script" ]]; then
        cp "$new_script" "$0"
        chmod +x "$0"
        rm -f "$new_script"
        log_success "脚本更新完成"
        echo "请重新运行脚本以使用新版本"
    else
        log_error "脚本更新失败"
        return 1
    fi
}

# 快速状态检查
quick_status() {
    if systemctl list-unit-files 2>/dev/null | grep -q docker-cloud-agent; then
        local status=$(systemctl is-active docker-cloud-agent 2>/dev/null || echo "inactive")
        if [[ "$status" == "active" ]]; then
            echo -e "${GREEN}✓ 被控端服务运行中${NC}"
        else
            echo -e "${RED}✗ 被控端服务未运行${NC}"
        fi
    else
        echo -e "${YELLOW}! 被控端服务未安装${NC}"
    fi
}

# 主函数
main() {
    local action="${1:-menu}"
    
    # 显示快速状态（除了help和menu）
    if [[ "$action" != "help" && "$action" != "menu" && "$action" != "-h" && "$action" != "--help" ]]; then
        quick_status
        echo ""
    fi
    
    case "$action" in
        "install")
            show_banner
            check_root
            check_network
            execute_script "install_agent.sh" "install"
            ;;
        "test")
            show_banner
            check_network
            execute_script "test_installation.sh"
            ;;
        "uninstall")
            show_banner
            check_root
            check_network
            execute_script "uninstall_agent.sh"
            ;;
        "status")
            show_banner
            check_network
            execute_script "install_agent.sh" "status"
            ;;
        "logs")
            show_banner
            check_network
            execute_script "install_agent.sh" "logs"
            ;;
        "restart")
            show_banner
            check_root
            check_network
            execute_script "install_agent.sh" "restart"
            ;;
        "update")
            show_banner
            check_network
            do_update
            ;;
        "menu")
            check_network
            show_menu
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $action"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
