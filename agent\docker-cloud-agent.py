#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Docker容器云管理系统 - 被控端主程序
支持完整的Docker容器管理功能
"""

import os
import sys
import time
import json
import hashlib
import requests
import subprocess
import threading
import docker
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/docker-cloud-agent.log'),
        logging.StreamHandler()
    ]
)

class DockerCloudAgent:
    def __init__(self):
        self.load_config()
        self.session = requests.Session()
        self.session.timeout = 30
        self.running = True
        
        # 初始化Docker客户端
        try:
            self.docker_client = docker.from_env()
            logging.info("Docker客户端初始化成功")
        except Exception as e:
            logging.error(f"Docker客户端初始化失败: {e}")
            self.docker_client = None
        
    def load_config(self):
        """加载配置文件"""
        config_file = '/opt/docker-cloud-agent/auth.conf'
        if not os.path.exists(config_file):
            logging.error("配置文件不存在")
            sys.exit(1)
            
        with open(config_file, 'r') as f:
            for line in f:
                if '=' in line:
                    key, value = line.strip().split('=', 1)
                    setattr(self, key.lower(), value)
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # CPU信息
            cpu_info = subprocess.check_output("nproc", shell=True).decode().strip()
            
            # 内存信息
            mem_total = subprocess.check_output("free -m | grep Mem | awk '{print $2}'", shell=True).decode().strip()
            mem_used = subprocess.check_output("free -m | grep Mem | awk '{print $3}'", shell=True).decode().strip()
            mem_free = subprocess.check_output("free -m | grep Mem | awk '{print $4}'", shell=True).decode().strip()
            
            # 磁盘信息
            disk_total = subprocess.check_output("df -h / | tail -1 | awk '{print $2}'", shell=True).decode().strip()
            disk_used = subprocess.check_output("df -h / | tail -1 | awk '{print $3}'", shell=True).decode().strip()
            disk_free = subprocess.check_output("df -h / | tail -1 | awk '{print $4}'", shell=True).decode().strip()
            
            # Docker版本
            docker_version = subprocess.check_output("docker --version", shell=True).decode().strip()
            
            # 负载信息
            load_avg = subprocess.check_output("uptime | awk -F'load average:' '{print $2}'", shell=True).decode().strip()
            
            return {
                'cpu_cores': cpu_info,
                'memory': {
                    'total_mb': mem_total,
                    'used_mb': mem_used,
                    'free_mb': mem_free,
                    'usage_percent': round(int(mem_used) / int(mem_total) * 100, 2)
                },
                'disk': {
                    'total': disk_total,
                    'used': disk_used,
                    'free': disk_free
                },
                'docker_version': docker_version,
                'hostname': os.uname().nodename,
                'uptime': subprocess.check_output("uptime -p", shell=True).decode().strip(),
                'load_average': load_avg.strip(),
                'timestamp': int(time.time())
            }
        except Exception as e:
            logging.error(f"获取系统信息失败: {e}")
            return {}
    
    def get_docker_info(self):
        """获取Docker详细信息"""
        try:
            if not self.docker_client:
                return {'error': 'Docker客户端未初始化'}
            
            # Docker系统信息
            docker_info = self.docker_client.info()
            
            # 容器信息
            containers = self.docker_client.containers.list(all=True)
            container_info = []
            
            for container in containers:
                try:
                    stats = container.stats(stream=False)
                    
                    # 计算CPU使用率
                    cpu_percent = 0
                    if 'cpu_stats' in stats and 'precpu_stats' in stats:
                        cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - stats['precpu_stats']['cpu_usage']['total_usage']
                        system_delta = stats['cpu_stats']['system_cpu_usage'] - stats['precpu_stats']['system_cpu_usage']
                        if system_delta > 0:
                            cpu_percent = (cpu_delta / system_delta) * len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100
                    
                    # 计算内存使用率
                    memory_usage = 0
                    memory_limit = 0
                    if 'memory_stats' in stats:
                        memory_usage = stats['memory_stats'].get('usage', 0)
                        memory_limit = stats['memory_stats'].get('limit', 0)
                    
                    container_info.append({
                        'id': container.id[:12],
                        'name': container.name,
                        'image': container.image.tags[0] if container.image.tags else container.image.id[:12],
                        'status': container.status,
                        'state': container.attrs['State'],
                        'created': container.attrs['Created'],
                        'ports': container.attrs['NetworkSettings']['Ports'],
                        'cpu_percent': round(cpu_percent, 2),
                        'memory_usage_mb': round(memory_usage / 1024 / 1024, 2),
                        'memory_limit_mb': round(memory_limit / 1024 / 1024, 2),
                        'memory_percent': round(memory_usage / memory_limit * 100, 2) if memory_limit > 0 else 0
                    })
                except Exception as e:
                    logging.warning(f"获取容器 {container.name} 统计信息失败: {e}")
                    container_info.append({
                        'id': container.id[:12],
                        'name': container.name,
                        'image': container.image.tags[0] if container.image.tags else container.image.id[:12],
                        'status': container.status,
                        'error': str(e)
                    })
            
            # 镜像信息
            images = self.docker_client.images.list()
            image_info = []
            for image in images:
                image_info.append({
                    'id': image.id[:12],
                    'tags': image.tags,
                    'size_mb': round(image.attrs['Size'] / 1024 / 1024, 2),
                    'created': image.attrs['Created']
                })
            
            # 网络信息
            networks = self.docker_client.networks.list()
            network_info = []
            for network in networks:
                network_info.append({
                    'id': network.id[:12],
                    'name': network.name,
                    'driver': network.attrs['Driver'],
                    'scope': network.attrs['Scope']
                })
            
            # 卷信息
            volumes = self.docker_client.volumes.list()
            volume_info = []
            for volume in volumes:
                volume_info.append({
                    'name': volume.name,
                    'driver': volume.attrs['Driver'],
                    'mountpoint': volume.attrs['Mountpoint']
                })
            
            return {
                'docker_info': {
                    'version': docker_info.get('ServerVersion'),
                    'containers_total': docker_info.get('Containers', 0),
                    'containers_running': docker_info.get('ContainersRunning', 0),
                    'containers_paused': docker_info.get('ContainersPaused', 0),
                    'containers_stopped': docker_info.get('ContainersStopped', 0),
                    'images_total': docker_info.get('Images', 0),
                    'storage_driver': docker_info.get('Driver'),
                    'memory_limit': docker_info.get('MemTotal', 0),
                    'cpu_count': docker_info.get('NCPU', 0)
                },
                'containers': container_info,
                'images': image_info,
                'networks': network_info,
                'volumes': volume_info
            }
        except Exception as e:
            logging.error(f"获取Docker信息失败: {e}")
            return {'error': str(e)}
    
    def execute_command(self, command):
        """执行系统命令"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=300
            )
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'stdout': '',
                'stderr': '命令执行超时',
                'returncode': -1
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'returncode': -1
            }
    
    def execute_docker_command(self, action, params=None):
        """执行Docker相关命令"""
        try:
            if not self.docker_client:
                return {'success': False, 'error': 'Docker客户端未初始化'}
            
            params = params or {}
            
            if action == 'container_list':
                containers = self.docker_client.containers.list(all=params.get('all', True))
                return {
                    'success': True,
                    'data': [{'id': c.id[:12], 'name': c.name, 'status': c.status} for c in containers]
                }
            
            elif action == 'container_start':
                container_id = params.get('container_id')
                container = self.docker_client.containers.get(container_id)
                container.start()
                return {'success': True, 'message': f'容器 {container_id} 启动成功'}
            
            elif action == 'container_stop':
                container_id = params.get('container_id')
                container = self.docker_client.containers.get(container_id)
                container.stop()
                return {'success': True, 'message': f'容器 {container_id} 停止成功'}
            
            elif action == 'container_restart':
                container_id = params.get('container_id')
                container = self.docker_client.containers.get(container_id)
                container.restart()
                return {'success': True, 'message': f'容器 {container_id} 重启成功'}
            
            elif action == 'container_remove':
                container_id = params.get('container_id')
                force = params.get('force', False)
                container = self.docker_client.containers.get(container_id)
                container.remove(force=force)
                return {'success': True, 'message': f'容器 {container_id} 删除成功'}
            
            elif action == 'container_create':
                image = params.get('image')
                name = params.get('name')
                command = params.get('command')
                environment = params.get('environment', {})
                ports = params.get('ports', {})
                volumes = params.get('volumes', {})
                cpu_limit = params.get('cpu_limit')
                memory_limit = params.get('memory_limit')
                
                # 构建资源限制
                host_config = {}
                if cpu_limit:
                    host_config['nano_cpus'] = int(float(cpu_limit) * 1e9)
                if memory_limit:
                    host_config['mem_limit'] = memory_limit
                
                container = self.docker_client.containers.create(
                    image=image,
                    name=name,
                    command=command,
                    environment=environment,
                    ports=ports,
                    volumes=volumes,
                    host_config=self.docker_client.api.create_host_config(**host_config) if host_config else None
                )
                return {'success': True, 'container_id': container.id[:12], 'message': f'容器 {name} 创建成功'}
            
            elif action == 'container_update':
                container_id = params.get('container_id')
                cpu_limit = params.get('cpu_limit')
                memory_limit = params.get('memory_limit')
                
                update_config = {}
                if cpu_limit:
                    update_config['nano_cpus'] = int(float(cpu_limit) * 1e9)
                if memory_limit:
                    update_config['mem_limit'] = memory_limit
                
                self.docker_client.api.update_container(container_id, **update_config)
                return {'success': True, 'message': f'容器 {container_id} 配置更新成功'}
            
            elif action == 'image_list':
                images = self.docker_client.images.list()
                return {
                    'success': True,
                    'data': [{'id': img.id[:12], 'tags': img.tags, 'size': img.attrs['Size']} for img in images]
                }
            
            elif action == 'image_pull':
                image_name = params.get('image')
                image = self.docker_client.images.pull(image_name)
                return {'success': True, 'message': f'镜像 {image_name} 拉取成功'}
            
            elif action == 'image_remove':
                image_id = params.get('image_id')
                force = params.get('force', False)
                self.docker_client.images.remove(image_id, force=force)
                return {'success': True, 'message': f'镜像 {image_id} 删除成功'}
            
            else:
                return {'success': False, 'error': f'不支持的操作: {action}'}
                
        except Exception as e:
            logging.error(f"执行Docker命令失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def heartbeat(self):
        """心跳检测"""
        while self.running:
            try:
                data = {
                    'agent_id': self.agent_id,
                    'auth_user': self.auth_user,
                    'auth_pass': self.auth_pass,
                    'timestamp': int(time.time()),
                    'system_info': self.get_system_info(),
                    'docker_info': self.get_docker_info()
                }
                
                response = self.session.post(
                    f"{self.master_url}/api/heartbeat",
                    json=data,
                    verify=False
                )
                
                if response.status_code == 200:
                    logging.info("心跳发送成功")
                else:
                    logging.warning(f"心跳发送失败: {response.status_code}")
                    
            except Exception as e:
                logging.error(f"心跳发送异常: {e}")
            
            time.sleep(30)  # 30秒心跳间隔
    
    def poll_commands(self):
        """轮询命令"""
        while self.running:
            try:
                data = {
                    'agent_id': self.agent_id,
                    'auth_user': self.auth_user,
                    'auth_pass': self.auth_pass,
                    'timestamp': int(time.time())
                }
                
                response = self.session.post(
                    f"{self.master_url}/api/poll_command",
                    json=data,
                    verify=False
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('has_command'):
                        command_id = result.get('command_id')
                        command_type = result.get('command_type', 'system')
                        command = result.get('command')
                        params = result.get('params', {})
                        
                        logging.info(f"收到命令: {command_type} - {command}")
                        
                        # 根据命令类型执行不同的处理
                        if command_type == 'docker':
                            exec_result = self.execute_docker_command(command, params)
                        else:
                            exec_result = self.execute_command(command)
                        
                        # 返回执行结果
                        self.report_result(command_id, exec_result)
                        
            except Exception as e:
                logging.error(f"轮询命令异常: {e}")
            
            time.sleep(10)  # 10秒轮询间隔
    
    def report_result(self, command_id, result):
        """报告执行结果"""
        try:
            data = {
                'agent_id': self.agent_id,
                'auth_user': self.auth_user,
                'auth_pass': self.auth_pass,
                'command_id': command_id,
                'result': result,
                'timestamp': int(time.time())
            }
            
            response = self.session.post(
                f"{self.master_url}/api/report_result",
                json=data,
                verify=False
            )
            
            if response.status_code == 200:
                logging.info(f"结果报告成功: {command_id}")
            else:
                logging.warning(f"结果报告失败: {response.status_code}")
                
        except Exception as e:
            logging.error(f"结果报告异常: {e}")
    
    def test_connection(self):
        """测试与主控端的连接"""
        try:
            data = {
                'agent_id': self.agent_id,
                'auth_user': self.auth_user,
                'auth_pass': self.auth_pass,
                'timestamp': int(time.time()),
                'test': True
            }
            
            response = self.session.post(
                f"{self.master_url}/api/test_connection",
                json=data,
                verify=False,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                logging.info(f"连接测试成功: {result}")
                return {'success': True, 'data': result}
            else:
                logging.error(f"连接测试失败: {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logging.error(f"连接测试异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def run(self):
        """运行代理"""
        logging.info("Docker Cloud Agent 启动")
        
        # 测试连接
        conn_test = self.test_connection()
        if not conn_test['success']:
            logging.warning(f"初始连接测试失败: {conn_test['error']}")
        
        # 启动心跳线程
        heartbeat_thread = threading.Thread(target=self.heartbeat)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()
        
        # 启动命令轮询线程
        poll_thread = threading.Thread(target=self.poll_commands)
        poll_thread.daemon = True
        poll_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logging.info("收到停止信号")
            self.running = False

if __name__ == "__main__":
    agent = DockerCloudAgent()
    agent.run()
