# 📚 使用示例

## 🚀 场景1: 新服务器快速部署

```bash
# 1. 登录新的Ubuntu服务器
ssh root@your-server-ip

# 2. 一键安装被控端
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh | sudo bash -s install

# 3. 记录显示的鉴权信息
# 账号: agent_xxxxxxxx
# 密码: xxxxxxxxxxxxxxxx

# 4. 验证安装
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh | bash -s test
```

## 🔧 场景2: 使用管理脚本进行维护

```bash
# 1. 下载管理脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o dcm.sh
chmod +x dcm.sh

# 2. 使用交互式菜单
./dcm.sh menu

# 3. 或直接使用命令
./dcm.sh status    # 查看状态
./dcm.sh logs      # 查看日志
./dcm.sh test      # 运行测试
```

## 📊 场景3: 监控和故障排除

```bash
# 下载管理脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o dcm.sh
chmod +x dcm.sh

# 查看详细状态
./dcm.sh status

# 查看实时日志
./dcm.sh logs

# 如果服务异常，重启服务
sudo ./dcm.sh restart

# 运行完整测试
./dcm.sh test
```

## 🔄 场景4: 系统更新和维护

```bash
# 下载最新管理脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o dcm.sh
chmod +x dcm.sh

# 更新管理脚本本身
./dcm.sh update

# 测试系统状态
./dcm.sh test

# 如果需要重新安装
sudo ./dcm.sh uninstall
sudo ./dcm.sh install
```

## 🏢 场景5: 批量部署多台服务器

### 创建批量部署脚本

```bash
#!/bin/bash
# batch_deploy.sh

# 服务器列表
servers=(
    "192.168.1.10"
    "192.168.1.11"
    "192.168.1.12"
)

# 批量部署
for server in "${servers[@]}"; do
    echo "正在部署服务器: $server"
    
    ssh root@$server "curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh | bash -s install"
    
    echo "服务器 $server 部署完成"
    echo "------------------------"
done

echo "所有服务器部署完成！"
```

### 批量检查状态

```bash
#!/bin/bash
# batch_check.sh

servers=(
    "192.168.1.10"
    "192.168.1.11"
    "192.168.1.12"
)

for server in "${servers[@]}"; do
    echo "检查服务器: $server"
    
    ssh root@$server "curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh | bash -s status"
    
    echo "------------------------"
done
```

## 🐛 场景6: 故障诊断和修复

### 完整的故障诊断流程

```bash
# 1. 下载诊断脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o dcm.sh
chmod +x dcm.sh

# 2. 检查基本状态
./dcm.sh status

# 3. 运行完整测试
./dcm.sh test

# 4. 查看详细日志
./dcm.sh logs

# 5. 如果发现问题，尝试重启
sudo ./dcm.sh restart

# 6. 如果问题持续，重新安装
sudo ./dcm.sh uninstall
sudo ./dcm.sh install

# 7. 验证修复结果
./dcm.sh test
```

### 常见问题快速修复

```bash
# 问题1: 服务未运行
sudo systemctl start docker-cloud-agent
sudo systemctl enable docker-cloud-agent

# 问题2: Docker问题
sudo systemctl restart docker
sudo ./dcm.sh restart

# 问题3: 网络连接问题
ping docker.ly-y.cn
curl -I https://d.ly-y.cn

# 问题4: 配置文件损坏
sudo ./dcm.sh uninstall
sudo ./dcm.sh install
```

## 📱 场景7: 移动设备管理

### 使用手机SSH客户端

```bash
# 1. 连接服务器
ssh root@your-server-ip

# 2. 快速检查状态
curl -s https://d.ly-y.cn/d/docker-cloud-manager.sh | bash -s status

# 3. 查看日志
curl -s https://d.ly-y.cn/d/docker-cloud-manager.sh | bash -s logs

# 4. 重启服务（如需要）
curl -s https://d.ly-y.cn/d/docker-cloud-manager.sh | sudo bash -s restart
```

## 🔐 场景8: 安全管理

### 查看和备份鉴权信息

```bash
# 查看当前配置
cat /opt/docker-cloud-agent/auth.conf

# 备份配置文件
sudo cp /opt/docker-cloud-agent/auth.conf ~/docker-agent-backup-$(date +%Y%m%d).conf

# 查看服务状态
systemctl status docker-cloud-agent
```

### 更换鉴权信息（重新安装）

```bash
# 下载管理脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o dcm.sh
chmod +x dcm.sh

# 卸载旧系统
sudo ./dcm.sh uninstall

# 重新安装（会生成新的鉴权信息）
sudo ./dcm.sh install

# 记录新的鉴权信息
cat /opt/docker-cloud-agent/auth.conf
```

## 📈 场景9: 性能监控

### 系统资源监控

```bash
# 下载管理脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o dcm.sh
chmod +x dcm.sh

# 查看系统状态
./dcm.sh status

# 查看Docker状态
docker system df
docker system info

# 查看进程资源使用
ps aux | grep docker-cloud-agent
top -p $(pgrep -f docker-cloud-agent)
```

### 日志分析

```bash
# 查看最近的日志
./dcm.sh logs

# 分析错误日志
journalctl -u docker-cloud-agent --since "1 hour ago" | grep -i error

# 查看应用日志
tail -f /var/log/docker-cloud-agent.log
```

## 🎯 最佳实践

### 1. 定期维护

```bash
# 每周执行一次
./dcm.sh test
./dcm.sh update

# 每月执行一次
sudo ./dcm.sh restart
```

### 2. 监控脚本

```bash
#!/bin/bash
# monitor.sh - 放在crontab中每5分钟执行一次

if ! systemctl is-active --quiet docker-cloud-agent; then
    echo "$(date): 服务异常，尝试重启" >> /var/log/monitor.log
    systemctl restart docker-cloud-agent
fi
```

### 3. 备份策略

```bash
#!/bin/bash
# backup.sh - 每天备份配置

backup_dir="/backup/docker-agent/$(date +%Y%m%d)"
mkdir -p "$backup_dir"

cp /opt/docker-cloud-agent/auth.conf "$backup_dir/"
cp /var/log/docker-cloud-agent.log "$backup_dir/"

echo "备份完成: $backup_dir"
```
