# 🐳 Docker容器云管理系统 - 项目总览

## 📋 项目简介

Docker容器云管理系统是一个分布式容器管理平台，采用主控-被控架构，支持对多台服务器上的Docker容器进行统一管理和监控。

### 🏗️ 系统架构

```
┌─────────────────┐         ┌─────────────────┐
│   主控端        │         │   被控端1       │
│ docker.ly-y.cn  │◄────────┤ Ubuntu 22/24    │
│                 │         │ Docker + Agent  │
└─────────────────┘         └─────────────────┘
         │                           │
         │                  ┌─────────────────┐
         └──────────────────┤   被控端2       │
                            │ Ubuntu 22/24    │
                            │ Docker + Agent  │
                            └─────────────────┘
```

## 📁 项目文件结构

```
docker-cloud-management/
├── docker-cloud-manager.sh    # 🎯 统一管理脚本（主要文件）
├── install_agent.sh           # 📦 被控端安装脚本
├── test_installation.sh       # 🧪 安装测试脚本
├── uninstall_agent.sh         # 🗑️ 卸载脚本
├── README.md                  # 📖 详细说明文档
├── QUICK_INSTALL.md           # ⚡ 快速安装指南
├── USAGE_EXAMPLES.md          # 📚 使用示例
└── PROJECT_OVERVIEW.md        # 📋 项目总览（本文件）
```

## 🌐 下载地址

所有文件都可以从以下地址下载：

- **统一管理脚本**: https://d.ly-y.cn/d/docker-cloud-manager.sh
- **安装脚本**: https://d.ly-y.cn/d/install_agent.sh
- **测试脚本**: https://d.ly-y.cn/d/test_installation.sh
- **卸载脚本**: https://d.ly-y.cn/d/uninstall_agent.sh

## 🚀 快速开始

### 方法1: 一键安装（推荐）

```bash
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh | sudo bash -s install
```

### 方法2: 下载管理脚本

```bash
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o dcm.sh
chmod +x dcm.sh
sudo ./dcm.sh install
```

## 🎮 主要功能

### 🔧 被控端管理
- ✅ 一键安装被控端系统
- ✅ 自动配置Docker环境
- ✅ 生成安全鉴权信息
- ✅ 系统服务自动启动
- ✅ 进程守护保护

### 📊 监控功能
- ✅ 实时心跳检测（30秒间隔）
- ✅ 系统信息收集（CPU/内存/磁盘）
- ✅ 命令远程执行（10秒轮询）
- ✅ 日志记录和分析

### 🛠️ 运维功能
- ✅ 服务状态查看
- ✅ 日志实时监控
- ✅ 服务重启管理
- ✅ 系统测试验证
- ✅ 一键卸载清理

## 📋 系统要求

### 被控端要求
- **操作系统**: Ubuntu 22.04 LTS 或 Ubuntu 24.04 LTS
- **权限**: Root权限
- **网络**: 能够访问互联网和主控端
- **硬件**: 最低1GB内存，10GB磁盘空间

### 主控端要求
- **域名**: docker.ly-y.cn
- **协议**: HTTPS
- **API**: RESTful接口
- **认证**: 基于账号密码的鉴权机制

## 🔐 安全特性

### 鉴权机制
- 🔑 随机生成账号密码
- 🔒 配置文件权限保护（600）
- 🛡️ HTTPS加密传输
- 🔄 支持鉴权信息更新

### 网络安全
- 🌐 仅出站连接（被控端主动连接主控端）
- 📡 心跳检测确保连接有效性
- 🚫 无需开放入站端口
- 🔍 命令执行日志记录

## 📊 监控指标

### 系统指标
- CPU核心数和使用率
- 内存总量和使用情况
- 磁盘空间和使用率
- 系统运行时间
- 主机名和网络信息

### 服务指标
- Docker版本和状态
- 容器运行情况
- 镜像存储使用
- 服务运行状态
- 日志文件大小

## 🔄 工作流程

### 安装流程
1. 下载统一管理脚本
2. 执行安装命令
3. 自动安装依赖组件
4. 配置Docker镜像源
5. 生成鉴权信息
6. 启动被控端服务
7. 验证安装结果

### 运行流程
1. 被控端启动后开始心跳
2. 定期向主控端报告状态
3. 轮询主控端获取命令
4. 执行命令并返回结果
5. 记录操作日志
6. 异常时自动重启

## 🛠️ 技术栈

### 被控端技术
- **语言**: Python 3.8+
- **HTTP库**: requests
- **系统服务**: systemd
- **容器**: Docker CE
- **操作系统**: Ubuntu 22.04/24.04

### 主控端技术
- **域名**: docker.ly-y.cn
- **协议**: HTTPS/REST API
- **认证**: 基于Token的认证
- **数据格式**: JSON

## 📈 性能特点

### 资源占用
- **内存使用**: < 50MB
- **CPU使用**: < 1%
- **网络流量**: < 1KB/分钟（心跳）
- **磁盘使用**: < 100MB

### 响应时间
- **心跳间隔**: 30秒
- **命令轮询**: 10秒
- **命令执行**: 实时
- **状态更新**: 实时

## 🔧 故障处理

### 常见问题
1. **网络连接问题**: 检查防火墙和DNS
2. **权限问题**: 确保使用root权限
3. **系统版本问题**: 仅支持Ubuntu 22.04/24.04
4. **Docker问题**: 检查Docker服务状态

### 故障恢复
1. **服务异常**: 自动重启机制
2. **配置损坏**: 重新安装恢复
3. **网络中断**: 自动重连机制
4. **系统重启**: 服务自动启动

## 📞 技术支持

### 获取帮助
```bash
# 查看帮助信息
./docker-cloud-manager.sh help

# 运行系统测试
./docker-cloud-manager.sh test

# 查看详细状态
./docker-cloud-manager.sh status
```

### 问题报告
提供以下信息以获得更好的技术支持：
- 系统版本: `lsb_release -a`
- 服务状态: `./docker-cloud-manager.sh status`
- 错误日志: `./docker-cloud-manager.sh logs`
- 测试结果: `./docker-cloud-manager.sh test`

## 🔮 未来规划

### 功能扩展
- 🎯 Web管理界面
- 📊 图形化监控面板
- 🔔 告警通知机制
- 📦 容器编排支持
- 🔄 自动更新机制

### 平台支持
- 🐧 CentOS/RHEL支持
- 🪟 Windows容器支持
- ☁️ 云平台集成
- 🐳 Kubernetes集成

## 📄 许可证

本项目采用开源许可证，详情请查看LICENSE文件。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

---

**项目维护**: Docker Cloud Management Team  
**最后更新**: 2024年8月  
**版本**: v1.0.0
