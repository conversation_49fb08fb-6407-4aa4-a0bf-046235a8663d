#!/bin/bash

# Docker容器云管理系统 - 被控端卸载脚本
# 使用方法: sudo ./uninstall_agent.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行，请使用sudo执行"
        exit 1
    fi
}

# 确认卸载
confirm_uninstall() {
    echo "========================================"
    echo "Docker容器云管理系统被控端卸载脚本"
    echo "========================================"
    echo ""
    log_warning "此操作将完全卸载被控端程序和相关配置"
    echo ""
    echo "将要删除的内容："
    echo "- 被控端服务 (docker-cloud-agent)"
    echo "- 程序文件 (/opt/docker-cloud-agent)"
    echo "- 配置文件 (包含鉴权信息)"
    echo "- 日志文件 (/var/log/docker-cloud-agent.log)"
    echo "- 系统服务配置"
    echo ""
    log_warning "注意: Docker和Python3不会被卸载"
    echo ""
    
    read -p "确认要继续卸载吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "卸载已取消"
        exit 0
    fi
}

# 停止并禁用服务
stop_service() {
    log_info "停止被控端服务..."
    
    if systemctl is-active --quiet docker-cloud-agent; then
        systemctl stop docker-cloud-agent
        log_success "服务已停止"
    else
        log_warning "服务未运行"
    fi
    
    if systemctl is-enabled --quiet docker-cloud-agent; then
        systemctl disable docker-cloud-agent
        log_success "服务已禁用"
    else
        log_warning "服务未启用"
    fi
}

# 删除系统服务文件
remove_service_file() {
    log_info "删除系统服务文件..."
    
    if [[ -f /etc/systemd/system/docker-cloud-agent.service ]]; then
        rm -f /etc/systemd/system/docker-cloud-agent.service
        systemctl daemon-reload
        log_success "系统服务文件已删除"
    else
        log_warning "系统服务文件不存在"
    fi
}

# 删除程序文件
remove_program_files() {
    log_info "删除程序文件..."
    
    if [[ -d /opt/docker-cloud-agent ]]; then
        # 备份配置文件（如果用户需要）
        if [[ -f /opt/docker-cloud-agent/auth.conf ]]; then
            log_info "备份配置文件到 /tmp/docker-cloud-agent-backup.conf"
            cp /opt/docker-cloud-agent/auth.conf /tmp/docker-cloud-agent-backup.conf
            chmod 600 /tmp/docker-cloud-agent-backup.conf
        fi
        
        rm -rf /opt/docker-cloud-agent
        log_success "程序文件已删除"
    else
        log_warning "程序目录不存在"
    fi
}

# 删除日志文件
remove_log_files() {
    log_info "删除日志文件..."
    
    if [[ -f /var/log/docker-cloud-agent.log ]]; then
        rm -f /var/log/docker-cloud-agent.log
        log_success "日志文件已删除"
    else
        log_warning "日志文件不存在"
    fi
}

# 清理Python依赖（可选）
cleanup_python_deps() {
    log_info "检查Python依赖..."
    
    # 检查是否有其他程序使用requests模块
    if python3 -c "import requests" 2>/dev/null; then
        log_warning "requests模块仍然存在，可能被其他程序使用，不会删除"
    fi
}

# 检查残留进程
check_processes() {
    log_info "检查残留进程..."
    
    if pgrep -f "docker-cloud-agent" >/dev/null; then
        log_warning "发现残留进程，正在终止..."
        pkill -f "docker-cloud-agent" || true
        sleep 2
        
        if pgrep -f "docker-cloud-agent" >/dev/null; then
            log_warning "强制终止残留进程..."
            pkill -9 -f "docker-cloud-agent" || true
        fi
        
        log_success "进程清理完成"
    else
        log_success "没有发现残留进程"
    fi
}

# 验证卸载结果
verify_uninstall() {
    log_info "验证卸载结果..."
    
    local issues=0
    
    # 检查服务
    if systemctl list-unit-files | grep -q docker-cloud-agent; then
        log_error "系统服务仍然存在"
        issues=$((issues + 1))
    fi
    
    # 检查程序目录
    if [[ -d /opt/docker-cloud-agent ]]; then
        log_error "程序目录仍然存在"
        issues=$((issues + 1))
    fi
    
    # 检查进程
    if pgrep -f "docker-cloud-agent" >/dev/null; then
        log_error "进程仍在运行"
        issues=$((issues + 1))
    fi
    
    # 检查日志文件
    if [[ -f /var/log/docker-cloud-agent.log ]]; then
        log_error "日志文件仍然存在"
        issues=$((issues + 1))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log_success "卸载验证通过"
        return 0
    else
        log_error "发现 $issues 个问题"
        return 1
    fi
}

# 显示卸载结果
show_result() {
    echo ""
    echo "========================================"
    echo "卸载完成"
    echo "========================================"
    echo ""
    
    if verify_uninstall; then
        echo -e "${GREEN}✅ 被控端已完全卸载${NC}"
        echo ""
        echo "已删除的内容："
        echo "- 被控端服务"
        echo "- 程序文件"
        echo "- 配置文件"
        echo "- 日志文件"
        echo ""
        
        if [[ -f /tmp/docker-cloud-agent-backup.conf ]]; then
            echo "配置文件备份位置: /tmp/docker-cloud-agent-backup.conf"
            echo ""
        fi
        
        echo "保留的内容："
        echo "- Docker (如需卸载请手动执行)"
        echo "- Python3 (如需卸载请手动执行)"
        echo ""
        
        echo "如需重新安装，请重新运行安装脚本"
    else
        echo -e "${RED}❌ 卸载过程中发现问题${NC}"
        echo ""
        echo "请手动检查并清理残留文件"
        echo ""
        echo "手动清理命令："
        echo "systemctl stop docker-cloud-agent"
        echo "systemctl disable docker-cloud-agent"
        echo "rm -f /etc/systemd/system/docker-cloud-agent.service"
        echo "rm -rf /opt/docker-cloud-agent"
        echo "rm -f /var/log/docker-cloud-agent.log"
        echo "pkill -f docker-cloud-agent"
        echo "systemctl daemon-reload"
    fi
    
    echo "========================================"
}

# 主函数
main() {
    check_root
    confirm_uninstall
    
    echo ""
    log_info "开始卸载被控端..."
    echo ""
    
    stop_service
    check_processes
    remove_service_file
    remove_program_files
    remove_log_files
    cleanup_python_deps
    
    show_result
}

# 执行主函数
main
