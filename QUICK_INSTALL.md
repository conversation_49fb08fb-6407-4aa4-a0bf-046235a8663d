# 快速安装指南

## 一键安装命令

```bash
# 下载并执行安装脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/docker-cloud-agent/main/install_agent.sh | sudo bash

# 或者分步执行
wget https://raw.githubusercontent.com/your-repo/docker-cloud-agent/main/install_agent.sh
chmod +x install_agent.sh
sudo ./install_agent.sh
```

## 安装后验证

```bash
# 下载测试脚本
wget https://raw.githubusercontent.com/your-repo/docker-cloud-agent/main/test_installation.sh
chmod +x test_installation.sh
sudo ./test_installation.sh
```

## 重要信息记录

安装完成后，请记录以下信息用于主控端配置：

```
节点IP: ___________________
ROOT用户: root
ROOT密码: ___________________
鉴权账号: ___________________
鉴权密码: ___________________
```

## 常用管理命令

```bash
# 查看服务状态
systemctl status docker-cloud-agent

# 查看实时日志
journalctl -u docker-cloud-agent -f

# 重启服务
systemctl restart docker-cloud-agent

# 查看配置
cat /opt/docker-cloud-agent/auth.conf
```

## 支持的系统

- ✅ Ubuntu 22.04 LTS
- ✅ Ubuntu 24.04 LTS

## 安装内容

- ✅ Python3 + pip
- ✅ Docker CE + Compose
- ✅ 阿里云镜像源
- ✅ 被控端程序
- ✅ 系统服务
- ✅ 进程守护

## 故障排除

如果安装失败，请检查：

1. **系统版本**: 必须是Ubuntu 22.04或24.04
2. **网络连接**: 确保能访问互联网
3. **权限**: 必须使用sudo或root执行
4. **磁盘空间**: 确保有足够的磁盘空间

## 技术支持

遇到问题请提供：
- 系统版本: `lsb_release -a`
- 错误日志: `journalctl -u docker-cloud-agent`
- 测试结果: `sudo ./test_installation.sh`
