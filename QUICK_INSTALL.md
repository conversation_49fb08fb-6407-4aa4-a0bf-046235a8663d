# 🚀 快速安装指南

## ⚡ 超级一键安装

```bash
# 方法1: 直接安装（推荐）
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh | sudo bash -s install

# 方法2: 使用wget
wget -qO- https://d.ly-y.cn/d/docker-cloud-manager.sh | sudo bash -s install
```

## 📥 下载管理脚本

```bash
# 下载统一管理脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o docker-cloud-manager.sh
chmod +x docker-cloud-manager.sh

# 使用交互式菜单
./docker-cloud-manager.sh menu

# 或直接执行安装
sudo ./docker-cloud-manager.sh install
```

## ✅ 安装后验证

```bash
# 自动测试安装状态
./docker-cloud-manager.sh test

# 查看服务状态
./docker-cloud-manager.sh status
```

## 📋 重要信息记录

安装完成后，请记录以下信息用于主控端配置：

```
节点IP: ___________________
ROOT用户: root
ROOT密码: ___________________
鉴权账号: ___________________
鉴权密码: ___________________
```

## 🎮 管理命令大全

```bash
# 基本操作
./docker-cloud-manager.sh install     # 安装被控端
./docker-cloud-manager.sh test        # 测试安装
./docker-cloud-manager.sh status      # 查看状态
./docker-cloud-manager.sh logs        # 查看日志
./docker-cloud-manager.sh restart     # 重启服务
./docker-cloud-manager.sh uninstall   # 卸载系统
./docker-cloud-manager.sh update      # 更新脚本
./docker-cloud-manager.sh menu        # 交互菜单
./docker-cloud-manager.sh help        # 显示帮助

# 传统系统命令
systemctl status docker-cloud-agent   # 查看服务状态
journalctl -u docker-cloud-agent -f   # 查看实时日志
systemctl restart docker-cloud-agent  # 重启服务
cat /opt/docker-cloud-agent/auth.conf # 查看配置
```

## 🖥️ 支持的系统

- ✅ Ubuntu 22.04 LTS
- ✅ Ubuntu 24.04 LTS

## 📦 安装内容

- ✅ Python3 + pip + requests
- ✅ Docker CE + Compose Plugin
- ✅ 阿里云镜像源配置
- ✅ 被控端程序 + 配置
- ✅ 系统服务 + 自启动
- ✅ 进程守护 + 日志记录

## 🔧 故障排除

### 安装失败检查清单

1. **系统版本**: 必须是Ubuntu 22.04或24.04
   ```bash
   lsb_release -a
   ```

2. **网络连接**: 确保能访问下载服务器
   ```bash
   curl -I https://d.ly-y.cn/d/docker-cloud-manager.sh
   ```

3. **权限**: 必须使用sudo或root执行安装
   ```bash
   whoami  # 应该显示root（使用sudo时）
   ```

4. **磁盘空间**: 确保有足够的磁盘空间
   ```bash
   df -h /
   ```

### 常见问题解决

```bash
# 问题1: 下载失败
sudo apt update && sudo apt install -y curl wget

# 问题2: 服务启动失败
./docker-cloud-manager.sh logs

# 问题3: 网络连接问题
ping docker.ly-y.cn

# 问题4: 重新安装
sudo ./docker-cloud-manager.sh uninstall
sudo ./docker-cloud-manager.sh install
```

## 🆘 技术支持

遇到问题请提供以下信息：

```bash
# 系统信息
lsb_release -a

# 服务状态
./docker-cloud-manager.sh status

# 测试结果
./docker-cloud-manager.sh test

# 错误日志
./docker-cloud-manager.sh logs
```

## 🔄 更新和维护

```bash
# 更新管理脚本
./docker-cloud-manager.sh update

# 重新测试系统
./docker-cloud-manager.sh test

# 查看最新状态
./docker-cloud-manager.sh status
```
