# Docker容器云管理系统 - 被控端

这是一个Docker容器云管理系统的被控端管理工具，用于在Ubuntu服务器上部署、管理和维护被控节点。

## 🚀 一键安装

### 方法1: 一键安装命令
```bash
# 下载并直接安装
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh | sudo bash -s install

# 或者使用wget
wget -qO- https://d.ly-y.cn/d/docker-cloud-manager.sh | sudo bash -s install
```

### 方法2: 下载管理脚本
```bash
# 下载管理脚本
curl -fsSL https://d.ly-y.cn/d/docker-cloud-manager.sh -o docker-cloud-manager.sh
chmod +x docker-cloud-manager.sh

# 使用交互式菜单
./docker-cloud-manager.sh menu

# 或直接安装
sudo ./docker-cloud-manager.sh install
```

## 📋 系统要求

- **操作系统**: Ubuntu 22.04 或 Ubuntu 24.04
- **权限**: Root权限（安装、卸载、重启服务时需要）
- **网络**: 能够访问互联网和主控端服务器 (docker.ly-y.cn)
- **工具**: curl 或 wget（用于下载脚本）

## ✨ 功能特性

### 🔧 自动安装组件
- ✅ Python3 及相关包 (requests)
- ✅ Docker CE 最新版本
- ✅ Docker Compose Plugin
- ✅ 必要的系统依赖

### ⚙️ 配置优化
- ✅ Docker阿里云镜像源配置
- ✅ Docker日志轮转配置
- ✅ 系统服务自动启动
- ✅ 进程守护保护

### 🔒 安全特性
- ✅ 随机生成鉴权账号密码
- ✅ 配置文件权限保护 (600)
- ✅ 安全的通信机制
- ✅ 加密传输支持

### 📊 监控功能
- ✅ 系统信息收集 (CPU/内存/磁盘)
- ✅ 心跳检测机制 (30秒间隔)
- ✅ 命令轮询执行 (10秒间隔)
- ✅ 实时日志记录

## 🎯 管理命令

### 基本操作
```bash
# 安装被控端
sudo ./docker-cloud-manager.sh install

# 测试安装状态
./docker-cloud-manager.sh test

# 查看服务状态
./docker-cloud-manager.sh status

# 查看服务日志
./docker-cloud-manager.sh logs

# 重启服务
sudo ./docker-cloud-manager.sh restart

# 卸载系统
sudo ./docker-cloud-manager.sh uninstall

# 更新脚本
./docker-cloud-manager.sh update

# 交互式菜单
./docker-cloud-manager.sh menu

# 显示帮助
./docker-cloud-manager.sh help
```

## 📝 安装完成后

### 记录鉴权信息

安装完成后，脚本会显示类似以下的鉴权信息：

```
========================================
Docker容器云管理系统被控端安装完成！
========================================

鉴权信息：
- 账号: agent_a1b2c3d4
- 密码: Kx9mP2nQ7wE5tY8u

配置文件位置: /opt/docker-cloud-agent/auth.conf
日志文件位置: /var/log/docker-cloud-agent.log
========================================
```

**⚠️ 请务必记录这些鉴权信息，用于在主控端添加节点！**

### 验证安装

```bash
# 运行测试脚本验证安装
./docker-cloud-manager.sh test

# 查看服务状态
./docker-cloud-manager.sh status
```

## 服务管理

### 查看服务状态
```bash
systemctl status docker-cloud-agent
```

### 重启服务
```bash
systemctl restart docker-cloud-agent
```

### 停止服务
```bash
systemctl stop docker-cloud-agent
```

### 启动服务
```bash
systemctl start docker-cloud-agent
```

### 查看实时日志
```bash
journalctl -u docker-cloud-agent -f
```

### 查看历史日志
```bash
tail -f /var/log/docker-cloud-agent.log
```

## 配置文件

### 主配置文件
位置: `/opt/docker-cloud-agent/auth.conf`

```bash
AUTH_USER=agent_a1b2c3d4
AUTH_PASS=Kx9mP2nQ7wE5tY8u
AGENT_ID=hostname-1234567890
MASTER_URL=https://docker.ly-y.cn
```

### Docker配置文件
位置: `/etc/docker/daemon.json`

```json
{
    "registry-mirrors": [
        "https://registry.cn-hangzhou.aliyuncs.com",
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2"
}
```

## 主控端配置

在主控端添加节点时，需要提供以下信息：

1. **节点IP地址**: 被控端服务器的IP地址
2. **ROOT用户名**: 通常为 `root`
3. **ROOT密码**: 服务器的root密码
4. **鉴权账号**: 安装时生成的 `AUTH_USER`
5. **鉴权密码**: 安装时生成的 `AUTH_PASS`

## 网络要求

被控端需要能够访问以下地址：

- **主控端**: `https://docker.ly-y.cn`
- **Docker镜像源**: 阿里云、中科大、网易镜像源
- **系统更新源**: Ubuntu官方源

## 故障排除

### 1. 安装失败

检查系统版本：
```bash
lsb_release -a
```

检查网络连接：
```bash
ping docker.ly-y.cn
```

### 2. 服务启动失败

查看详细错误：
```bash
journalctl -u docker-cloud-agent --no-pager
```

检查配置文件：
```bash
cat /opt/docker-cloud-agent/auth.conf
```

### 3. 无法连接主控端

检查防火墙设置：
```bash
ufw status
```

检查网络连通性：
```bash
curl -k https://docker.ly-y.cn/api/heartbeat
```

### 4. Docker相关问题

检查Docker状态：
```bash
systemctl status docker
```

测试Docker功能：
```bash
docker run hello-world
```

## 卸载方法

如需卸载被控端，执行以下命令：

```bash
# 停止并禁用服务
systemctl stop docker-cloud-agent
systemctl disable docker-cloud-agent

# 删除服务文件
rm -f /etc/systemd/system/docker-cloud-agent.service

# 删除程序文件
rm -rf /opt/docker-cloud-agent

# 删除日志文件
rm -f /var/log/docker-cloud-agent.log

# 重新加载systemd
systemctl daemon-reload
```

## 技术支持

如遇到问题，请提供以下信息：

1. 系统版本: `lsb_release -a`
2. 错误日志: `journalctl -u docker-cloud-agent --no-pager`
3. 配置信息: `cat /opt/docker-cloud-agent/auth.conf`
4. 网络状态: `ping docker.ly-y.cn`

## 更新日志

- **v1.0.0**: 初始版本，支持Ubuntu 22.04/24.04
- 自动安装Docker和Python3
- 配置阿里云镜像源
- 实现心跳检测和命令轮询
- 添加进程守护保护
