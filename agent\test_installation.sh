#!/bin/bash

# Docker容器云管理系统 - 被控端安装测试脚本
# 使用方法: sudo ./test_installation.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_item() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "测试 $TOTAL_TESTS: $test_name ... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        if [[ "$expected_result" == "success" ]]; then
            echo -e "${GREEN}通过${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}失败${NC} (期望失败但成功了)"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        if [[ "$expected_result" == "fail" ]]; then
            echo -e "${GREEN}通过${NC} (期望失败)"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}失败${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    fi
}

# 显示详细信息的测试函数
test_with_output() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}测试 $TOTAL_TESTS: $test_name${NC}"
    
    if result=$(eval "$test_command" 2>&1); then
        echo -e "${GREEN}✓ 通过${NC}"
        echo "  结果: $result"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 失败${NC}"
        echo "  错误: $result"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

echo "========================================"
echo "Docker容器云管理系统被控端安装测试"
echo "========================================"
echo ""

# 1. 系统基础测试
echo -e "${YELLOW}=== 系统基础测试 ===${NC}"
test_item "检查操作系统" "grep -q 'Ubuntu' /etc/os-release" "success"
test_item "检查系统版本" "grep -E '22\.04|24\.04' /etc/os-release" "success"
test_item "检查root权限" "[[ \$EUID -eq 0 ]]" "success"
echo ""

# 2. Python安装测试
echo -e "${YELLOW}=== Python安装测试 ===${NC}"
test_with_output "Python3版本" "python3 --version"
test_item "pip3可用性" "which pip3" "success"
test_item "requests模块" "python3 -c 'import requests'" "success"
echo ""

# 3. Docker安装测试
echo -e "${YELLOW}=== Docker安装测试 ===${NC}"
test_with_output "Docker版本" "docker --version"
test_item "Docker服务状态" "systemctl is-active docker" "success"
test_item "Docker Compose插件" "docker compose version" "success"
test_item "Docker镜像源配置" "[[ -f /etc/docker/daemon.json ]]" "success"
echo ""

# 4. 被控端程序测试
echo -e "${YELLOW}=== 被控端程序测试 ===${NC}"
test_item "程序目录存在" "[[ -d /opt/docker-cloud-agent ]]" "success"
test_item "主程序文件存在" "[[ -f /opt/docker-cloud-agent/agent.py ]]" "success"
test_item "配置文件存在" "[[ -f /opt/docker-cloud-agent/auth.conf ]]" "success"
test_item "主程序可执行" "[[ -x /opt/docker-cloud-agent/agent.py ]]" "success"
echo ""

# 5. 系统服务测试
echo -e "${YELLOW}=== 系统服务测试 ===${NC}"
test_item "服务文件存在" "[[ -f /etc/systemd/system/docker-cloud-agent.service ]]" "success"
test_item "服务已启用" "systemctl is-enabled docker-cloud-agent" "success"
test_with_output "服务运行状态" "systemctl is-active docker-cloud-agent"
echo ""

# 6. 配置文件内容测试
echo -e "${YELLOW}=== 配置文件测试 ===${NC}"
if [[ -f /opt/docker-cloud-agent/auth.conf ]]; then
    echo "配置文件内容："
    cat /opt/docker-cloud-agent/auth.conf
    echo ""
    
    test_item "AUTH_USER配置" "grep -q 'AUTH_USER=' /opt/docker-cloud-agent/auth.conf" "success"
    test_item "AUTH_PASS配置" "grep -q 'AUTH_PASS=' /opt/docker-cloud-agent/auth.conf" "success"
    test_item "AGENT_ID配置" "grep -q 'AGENT_ID=' /opt/docker-cloud-agent/auth.conf" "success"
    test_item "MASTER_URL配置" "grep -q 'MASTER_URL=' /opt/docker-cloud-agent/auth.conf" "success"
else
    echo -e "${RED}配置文件不存在！${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 4))
    TOTAL_TESTS=$((TOTAL_TESTS + 4))
fi
echo ""

# 7. 网络连接测试
echo -e "${YELLOW}=== 网络连接测试 ===${NC}"
test_item "DNS解析" "nslookup docker.ly-y.cn" "success"
test_item "网络连通性" "ping -c 1 docker.ly-y.cn" "success"
echo ""

# 8. Docker功能测试
echo -e "${YELLOW}=== Docker功能测试 ===${NC}"
test_with_output "Docker信息" "docker info --format '{{.ServerVersion}}'"
test_item "Docker镜像拉取测试" "docker pull hello-world" "success"
test_item "Docker容器运行测试" "docker run --rm hello-world" "success"
echo ""

# 9. 日志文件测试
echo -e "${YELLOW}=== 日志文件测试 ===${NC}"
test_item "系统日志可访问" "journalctl -u docker-cloud-agent --no-pager -n 1" "success"
if [[ -f /var/log/docker-cloud-agent.log ]]; then
    test_item "应用日志文件存在" "true" "success"
    echo "最近的日志条目："
    tail -n 5 /var/log/docker-cloud-agent.log 2>/dev/null || echo "日志文件为空或无法读取"
else
    test_item "应用日志文件存在" "false" "fail"
fi
echo ""

# 10. 进程测试
echo -e "${YELLOW}=== 进程测试 ===${NC}"
if pgrep -f "docker-cloud-agent" >/dev/null; then
    test_item "被控端进程运行" "true" "success"
    echo "进程信息："
    ps aux | grep docker-cloud-agent | grep -v grep
else
    test_item "被控端进程运行" "false" "fail"
fi
echo ""

# 显示测试结果汇总
echo "========================================"
echo -e "${BLUE}测试结果汇总${NC}"
echo "========================================"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo ""
    echo -e "${GREEN}🎉 所有测试通过！被控端安装成功！${NC}"
    echo ""
    echo "接下来的步骤："
    echo "1. 记录配置文件中的鉴权信息"
    echo "2. 在主控端添加此节点"
    echo "3. 监控服务运行状态"
    echo ""
    echo "管理命令："
    echo "- 查看状态: systemctl status docker-cloud-agent"
    echo "- 查看日志: journalctl -u docker-cloud-agent -f"
    echo "- 重启服务: systemctl restart docker-cloud-agent"
else
    echo ""
    echo -e "${RED}❌ 发现 $FAILED_TESTS 个问题，请检查安装过程！${NC}"
    echo ""
    echo "故障排除建议："
    echo "1. 重新运行安装脚本"
    echo "2. 检查系统日志: journalctl -u docker-cloud-agent"
    echo "3. 检查网络连接"
    echo "4. 验证系统权限"
fi

echo "========================================"

# 返回适当的退出码
exit $FAILED_TESTS
